apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-orders
  namespace: kotak-demo
  labels:
    app: backend-orders
    component: orders
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend-orders
  template:
    metadata:
      labels:
        app: backend-orders
        component: orders
    spec:
      containers:
      - name: backend-orders
        image: kotak-backend-orders:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 3002
        env:
        - name: NODE_ENV
          value: "production"
        - name: INVENTORY_SERVICE_URL
          value: "http://backend-inventory:3003"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://$(OTEL_EXPORTER_OTLP_ENDPOINT):4317"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3002
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3002
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: backend-orders
  namespace: kotak-demo
  labels:
    app: backend-orders
spec:
  type: ClusterIP
  ports:
  - port: 3002
    targetPort: 3002
    protocol: TCP
  selector:
    app: backend-orders
---
apiVersion: v1
kind: Service
metadata:
  name: backend-orders-nodeport
  namespace: kotak-demo
  labels:
    app: backend-orders
spec:
  type: NodePort
  ports:
  - port: 3002
    targetPort: 3002
    nodePort: 30002
    protocol: TCP
  selector:
    app: backend-orders

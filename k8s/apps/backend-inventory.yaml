apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-inventory
  namespace: kotak-demo
  labels:
    app: backend-inventory
    component: inventory
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend-inventory
  template:
    metadata:
      labels:
        app: backend-inventory
        component: inventory
    spec:
      containers:
      - name: backend-inventory
        image: kotak-backend-inventory:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 3003
        env:
        - name: NODE_ENV
          value: "production"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://$(OTEL_EXPORTER_OTLP_ENDPOINT):4317"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3003
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3003
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: backend-inventory
  namespace: kotak-demo
  labels:
    app: backend-inventory
spec:
  type: ClusterIP
  ports:
  - port: 3003
    targetPort: 3003
    protocol: TCP
  selector:
    app: backend-inventory
---
apiVersion: v1
kind: Service
metadata:
  name: backend-inventory-nodeport
  namespace: kotak-demo
  labels:
    app: backend-inventory
spec:
  type: NodePort
  ports:
  - port: 3003
    targetPort: 3003
    nodePort: 30003
    protocol: TCP
  selector:
    app: backend-inventory

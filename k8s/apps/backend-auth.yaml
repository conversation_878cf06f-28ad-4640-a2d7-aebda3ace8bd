apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-auth
  namespace: kotak-demo
  labels:
    app: backend-auth
    component: auth
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend-auth
  template:
    metadata:
      labels:
        app: backend-auth
        component: auth
    spec:
      containers:
      - name: backend-auth
        image: kotak-backend-auth:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
        - name: JWT_SECRET
          value: "kotak-demo-jwt-secret-key"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://$(OTEL_EXPORTER_OTLP_ENDPOINT):4317"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: backend-auth
  namespace: kotak-demo
  labels:
    app: backend-auth
spec:
  type: ClusterIP
  ports:
  - port: 3001
    targetPort: 3001
    protocol: TCP
  selector:
    app: backend-auth
---
apiVersion: v1
kind: Service
metadata:
  name: backend-auth-nodeport
  namespace: kotak-demo
  labels:
    app: backend-auth
spec:
  type: NodePort
  ports:
  - port: 3001
    targetPort: 3001
    nodePort: 30001
    protocol: TCP
  selector:
    app: backend-auth

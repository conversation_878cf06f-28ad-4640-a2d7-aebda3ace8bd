#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_demo() {
    echo -e "${PURPLE}[DEMO]${NC} $1"
}

# Function to wait for user input
wait_for_user() {
    echo ""
    read -p "Press Enter to continue..."
    echo ""
}

# Demo introduction
demo_intro() {
    clear
    echo "=================================================================="
    echo "    Kotak Tail Sampling Demo - OpenTelemetry with Kubernetes"
    echo "=================================================================="
    echo ""
    echo "This demo will show you:"
    echo "• Kind Kubernetes cluster with 2 nodes"
    echo "• 4 distributed Node.js applications"
    echo "• OpenTelemetry auto-instrumentation"
    echo "• Coralogix integration"
    echo "• Tail sampling with different percentages per service"
    echo ""
    echo "Prerequisites:"
    echo "• Docker running"
    echo "• kind, kubectl, helm installed"
    echo "• CORALOGIX_API_KEY environment variable set"
    echo ""
    
    if [ -z "$CORALOGIX_API_KEY" ]; then
        print_error "CORALOGIX_API_KEY is not set!"
        echo "Please set it with: export CORALOGIX_API_KEY='your-api-key'"
        exit 1
    fi
    
    print_success "Environment looks good!"
    wait_for_user
}

# Step 1: Setup verification
demo_step1() {
    print_demo "Step 1: Verifying setup prerequisites"
    echo ""
    
    ./check-setup.sh
    
    wait_for_user
}

# Step 2: Create cluster and deploy
demo_step2() {
    print_demo "Step 2: Creating Kind cluster and deploying applications"
    echo ""
    
    print_status "This will:"
    echo "• Create a Kind cluster with 2 nodes"
    echo "• Build and load Docker images for 4 applications"
    echo "• Deploy applications to Kubernetes"
    echo "• Install OpenTelemetry with Coralogix integration"
    echo "• Configure tail sampling policies"
    echo ""
    
    wait_for_user
    
    ./setup.sh
    
    wait_for_user
}

# Step 3: Show cluster status
demo_step3() {
    print_demo "Step 3: Exploring the deployed cluster"
    echo ""
    
    print_status "Cluster nodes:"
    kubectl get nodes
    echo ""
    
    print_status "Application pods:"
    kubectl get pods -n kotak-demo
    echo ""
    
    print_status "Services:"
    kubectl get svc -n kotak-demo
    echo ""
    
    print_status "OpenTelemetry components:"
    kubectl get pods -l "app.kubernetes.io/name=opentelemetry-collector"
    echo ""
    
    wait_for_user
}

# Step 4: Test applications
demo_step4() {
    print_demo "Step 4: Testing the applications"
    echo ""
    
    print_status "Testing individual service health checks..."
    ./scripts/test-endpoints.sh test
    
    wait_for_user
}

# Step 5: Generate telemetry data
demo_step5() {
    print_demo "Step 5: Generating telemetry data for tail sampling"
    echo ""
    
    print_status "This will generate various types of traces:"
    echo "• Normal successful requests"
    echo "• Error traces (for 100% sampling demonstration)"
    echo "• Complex distributed traces across all services"
    echo "• Different volumes per service to show sampling differences"
    echo ""
    
    wait_for_user
    
    ./scripts/test-endpoints.sh load
    
    wait_for_user
}

# Step 6: Validate telemetry
demo_step6() {
    print_demo "Step 6: Validating telemetry setup"
    echo ""
    
    ./scripts/validate-telemetry.sh
    
    wait_for_user
}

# Step 7: Show tail sampling configuration
demo_step7() {
    print_demo "Step 7: Understanding tail sampling configuration"
    echo ""
    
    print_status "Tail Sampling Policies Applied:"
    echo ""
    echo "┌─────────────────┬─────────────┬─────────────┬──────────────────┐"
    echo "│ Service         │ Normal      │ Errors      │ Policy Type      │"
    echo "├─────────────────┼─────────────┼─────────────┼──────────────────┤"
    echo "│ Frontend        │ 50%         │ 100%        │ Probabilistic    │"
    echo "│ Auth Service    │ 75%         │ 100%        │ Probabilistic    │"
    echo "│ Orders Service  │ 25%         │ 100%        │ Probabilistic    │"
    echo "│ Inventory       │ 10%         │ 100%        │ Probabilistic    │"
    echo "│ Default         │ 20%         │ 100%        │ Probabilistic    │"
    echo "└─────────────────┴─────────────┴─────────────┴──────────────────┘"
    echo ""
    
    print_status "Key Benefits of Tail Sampling:"
    echo "• Decisions made after complete trace collection"
    echo "• Service-specific sampling rates"
    echo "• 100% sampling of error traces"
    echo "• Reduced storage costs while maintaining observability"
    echo "• Better trace completeness compared to head sampling"
    echo ""
    
    wait_for_user
}

# Step 8: Show monitoring
demo_step8() {
    print_demo "Step 8: Monitoring and observability"
    echo ""
    
    print_status "Access points:"
    echo "• Frontend Web UI: http://localhost:30000"
    echo "• Auth Service API: http://localhost:30001"
    echo "• Orders Service API: http://localhost:30002"
    echo "• Inventory Service API: http://localhost:30003"
    echo ""
    
    print_status "Useful monitoring commands:"
    echo "• kubectl logs -l 'app.kubernetes.io/component=opentelemetry-gateway' -f"
    echo "• kubectl get pods -n kotak-demo"
    echo "• ./scripts/test-endpoints.sh load  # Generate more data"
    echo ""
    
    print_status "Check your Coralogix dashboard for:"
    echo "• Traces from all 4 services"
    echo "• Different sampling rates per service"
    echo "• 100% of error traces"
    echo "• Service maps and dependencies"
    echo ""
    
    wait_for_user
}

# Demo conclusion
demo_conclusion() {
    print_demo "Demo completed successfully! 🎉"
    echo ""
    
    print_success "What you've accomplished:"
    echo "✅ Created a Kind Kubernetes cluster with 2 nodes"
    echo "✅ Deployed 4 distributed Node.js applications"
    echo "✅ Configured OpenTelemetry auto-instrumentation"
    echo "✅ Set up Coralogix integration"
    echo "✅ Implemented tail sampling with service-specific policies"
    echo "✅ Generated test telemetry data"
    echo "✅ Validated the complete setup"
    echo ""
    
    print_status "Next steps:"
    echo "• Explore your Coralogix dashboard"
    echo "• Experiment with different sampling policies"
    echo "• Generate more test data with ./scripts/test-endpoints.sh"
    echo "• Monitor OpenTelemetry components"
    echo "• Customize the applications for your use case"
    echo ""
    
    print_status "Cleanup when done:"
    echo "kind delete cluster --name kotak-tailsampling"
    echo ""
    
    print_success "Thank you for trying the Kotak Tail Sampling Demo!"
}

# Main demo flow
main() {
    case "${1:-full}" in
        "full")
            demo_intro
            demo_step1
            demo_step2
            demo_step3
            demo_step4
            demo_step5
            demo_step6
            demo_step7
            demo_step8
            demo_conclusion
            ;;
        "quick")
            demo_intro
            demo_step1
            demo_step2
            demo_step5
            demo_step6
            demo_conclusion
            ;;
        "test")
            demo_step4
            demo_step5
            ;;
        *)
            echo "Usage: $0 [full|quick|test]"
            echo "  full  - Complete demo walkthrough (default)"
            echo "  quick - Quick setup and test"
            echo "  test  - Just run tests and generate data"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"

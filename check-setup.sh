#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

print_status "Kotak Tail Sampling Demo - Setup Verification"
echo ""

# Check required tools
print_status "Checking required tools..."
tools=("kind" "kubectl" "helm" "docker" "curl")
missing_tools=()

for tool in "${tools[@]}"; do
    if command_exists "$tool"; then
        print_success "✓ $tool is installed"
    else
        print_error "✗ $tool is not installed"
        missing_tools+=("$tool")
    fi
done

if [ ${#missing_tools[@]} -ne 0 ]; then
    print_error "Missing required tools: ${missing_tools[*]}"
    print_error "Please install them before running the setup."
    exit 1
fi

echo ""

# Check Docker
print_status "Checking Docker..."
if docker info >/dev/null 2>&1; then
    print_success "✓ Docker is running"
else
    print_error "✗ Docker is not running"
    print_error "Please start Docker and try again."
    exit 1
fi

echo ""

# Check environment variables
print_status "Checking environment variables..."
if [ -n "$CORALOGIX_API_KEY" ]; then
    print_success "✓ CORALOGIX_API_KEY is set"
else
    print_error "✗ CORALOGIX_API_KEY is not set"
    print_error "Please set it with: export CORALOGIX_API_KEY='your-api-key'"
fi

if [ -n "$CORALOGIX_DOMAIN" ]; then
    print_success "✓ CORALOGIX_DOMAIN is set to: $CORALOGIX_DOMAIN"
else
    print_warning "⚠ CORALOGIX_DOMAIN is not set, will use default: coralogix.com"
fi

echo ""

# Check project structure
print_status "Checking project structure..."
required_files=(
    "setup.sh"
    "kind-cluster-config.yaml"
    "otel/coralogix-values.yaml"
    "k8s/namespace.yaml"
    "k8s/apps/frontend.yaml"
    "k8s/apps/backend-auth.yaml"
    "k8s/apps/backend-orders.yaml"
    "k8s/apps/backend-inventory.yaml"
    "apps/frontend/package.json"
    "apps/backend-auth/package.json"
    "apps/backend-orders/package.json"
    "apps/backend-inventory/package.json"
    "scripts/test-endpoints.sh"
    "scripts/validate-telemetry.sh"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "✓ $file exists"
    else
        print_error "✗ $file is missing"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    print_error "Missing required files: ${missing_files[*]}"
    exit 1
fi

echo ""

# Check if scripts are executable
print_status "Checking script permissions..."
scripts=("setup.sh" "scripts/test-endpoints.sh" "scripts/validate-telemetry.sh")
for script in "${scripts[@]}"; do
    if [ -x "$script" ]; then
        print_success "✓ $script is executable"
    else
        print_warning "⚠ $script is not executable, fixing..."
        chmod +x "$script"
        print_success "✓ $script is now executable"
    fi
done

echo ""

# Check if Kind cluster exists
print_status "Checking Kind cluster..."
if kind get clusters | grep -q "kotak-tailsampling"; then
    print_success "✓ Kind cluster 'kotak-tailsampling' exists"
    
    # Check if cluster is accessible
    if kubectl cluster-info --context kind-kotak-tailsampling >/dev/null 2>&1; then
        print_success "✓ Kind cluster is accessible"
    else
        print_warning "⚠ Kind cluster exists but is not accessible"
    fi
else
    print_warning "⚠ Kind cluster 'kotak-tailsampling' does not exist"
    print_status "Run './setup.sh' to create the cluster and deploy applications"
fi

echo ""

print_success "Setup verification completed!"
print_status "If all checks passed, you can run './setup.sh' to start the demo."
print_status "If the cluster already exists, you can run './scripts/test-endpoints.sh' to test the applications."

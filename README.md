# Kotak Tail Sampling - OpenTelemetry Kubernetes Demo

This project demonstrates a complete setup of:
- Kind Kubernetes cluster with 2 nodes
- 4 distributed Node.js applications with OpenTelemetry auto-instrumentation
- Coralogix integration for telemetry collection
- Tail sampling with different percentages for different services

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │───▶│  Auth Service   │───▶│ Orders Service  │───▶│Inventory Service│
│   (React/Node)  │    │   (Node.js)     │    │   (Node.js)     │    │   (Node.js)     │
│   Port: 3000    │    │   Port: 3001    │    │   Port: 3002    │    │   Port: 3003    │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │                       │
         └───────────────────────┼───────────────────────┼───────────────────────┘
                                 │                       │
                    ┌─────────────────────────────────────────────────────┐
                    │              OpenTelemetry                          │
                    │         ┌─────────────┐  ┌─────────────┐           │
                    │         │    Agent    │  │   Gateway   │           │
                    │         │ (DaemonSet) │  │(Deployment) │           │
                    │         └─────────────┘  └─────────────┘           │
                    │                    │                               │
                    │              Tail Sampling                        │
                    │         - Frontend: 50%                           │
                    │         - Auth: 75%                               │
                    │         - Orders: 25%                             │
                    │         - Inventory: 100% (errors)                │
                    └─────────────────────────────────────────────────────┘
                                         │
                                         ▼
                                 ┌─────────────┐
                                 │  Coralogix  │
                                 │   Backend   │
                                 └─────────────┘
```

## Quick Start

1. **Prerequisites:**
   ```bash
   # Install required tools
   brew install kind kubectl helm
   # or on Linux:
   # curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-amd64
   # chmod +x ./kind && sudo mv ./kind /usr/local/bin/kind
   ```

2. **Set up Coralogix API Key:**
   ```bash
   export CORALOGIX_API_KEY="your-coralogix-api-key"
   export CORALOGIX_DOMAIN="your-coralogix-domain"  # e.g., coralogix.com, eu2.coralogix.com
   ```

3. **Run the complete setup:**
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

4. **Test the applications:**
   ```bash
   chmod +x scripts/test-endpoints.sh
   ./scripts/test-endpoints.sh
   ```

## Manual Setup Steps

If you prefer to run steps manually:

1. [Create Kind cluster](#create-kind-cluster)
2. [Deploy applications](#deploy-applications)  
3. [Install OpenTelemetry](#install-opentelemetry)
4. [Validate setup](#validate-setup)

## Tail Sampling Configuration

The tail sampling is configured with different policies for each service:

- **Frontend Service**: 50% sampling rate
- **Auth Service**: 75% sampling rate  
- **Orders Service**: 25% sampling rate
- **Inventory Service**: 100% sampling for errors, 10% for normal traces

## Project Structure

```
.
├── README.md
├── setup.sh                    # Main automation script
├── kind-cluster-config.yaml    # Kind cluster configuration
├── apps/                       # Node.js applications
│   ├── frontend/
│   ├── backend-auth/
│   ├── backend-orders/
│   └── backend-inventory/
├── k8s/                        # Kubernetes manifests
│   ├── namespace.yaml
│   └── apps/
├── otel/                       # OpenTelemetry configuration
│   └── coralogix-values.yaml
└── scripts/                    # Testing and validation scripts
    ├── test-endpoints.sh
    └── validate-telemetry.sh
```

## Services Overview

### Frontend (Port 3000)
- React-based frontend with Node.js backend
- Makes calls to auth, orders, and inventory services
- Instrumented with OpenTelemetry auto-instrumentation

### Auth Service (Port 3001)  
- Handles user authentication
- Returns JWT tokens
- 75% tail sampling rate

### Orders Service (Port 3002)
- Manages order processing
- Calls inventory service for stock checks
- 25% tail sampling rate

### Inventory Service (Port 3003)
- Manages product inventory
- Simulates occasional errors for testing
- 100% sampling for errors, 10% for normal traces

## Monitoring and Observability

All services are auto-instrumented with OpenTelemetry to collect:
- **Traces**: Distributed tracing across all services
- **Metrics**: Application and infrastructure metrics  
- **Logs**: Structured application logs

Data is sent to Coralogix with tail sampling applied based on service-specific policies.

## Detailed Setup Instructions

### Create Kind Cluster

```bash
# Create the cluster with 2 nodes
kind create cluster --config kind-cluster-config.yaml

# Verify cluster is running
kubectl cluster-info --context kind-kotak-tailsampling
kubectl get nodes
```

### Deploy Applications

```bash
# Create namespace
kubectl apply -f k8s/namespace.yaml

# Deploy all applications
kubectl apply -f k8s/apps/

# Wait for pods to be ready
kubectl wait --for=condition=ready pod -l app=frontend -n kotak-demo --timeout=300s
kubectl wait --for=condition=ready pod -l app=backend-auth -n kotak-demo --timeout=300s
kubectl wait --for=condition=ready pod -l app=backend-orders -n kotak-demo --timeout=300s
kubectl wait --for=condition=ready pod -l app=backend-inventory -n kotak-demo --timeout=300s
```

### Install OpenTelemetry

```bash
# Add Coralogix Helm repository
helm repo add coralogix-charts-virtual https://cgx.jfrog.io/artifactory/coralogix-charts-virtual
helm repo update

# Create secret for Coralogix API key
kubectl create secret generic coralogix-keys \
    --from-literal="PRIVATE_KEY=$CORALOGIX_API_KEY" \
    --namespace=default

# Install OpenTelemetry integration with tail sampling
helm upgrade --install coralogix-opentelemetry \
    coralogix-charts-virtual/otel-integration \
    -f otel/coralogix-values.yaml \
    --set global.domain="$CORALOGIX_DOMAIN" \
    --wait --timeout=10m
```

### Validate Setup

```bash
# Run validation script
./scripts/validate-telemetry.sh

# Check all components are running
kubectl get pods -A
kubectl get svc -n kotak-demo
```

## Testing and Load Generation

### Basic Testing

```bash
# Test all endpoints
./scripts/test-endpoints.sh test

# Generate load for telemetry
./scripts/test-endpoints.sh load

# Run both tests and load generation
./scripts/test-endpoints.sh both
```

### Manual Testing

```bash
# Test individual services
curl http://localhost:30000/health  # Frontend
curl http://localhost:30001/health  # Auth
curl http://localhost:30002/health  # Orders
curl http://localhost:30003/health  # Inventory

# Test authentication
curl -X POST http://localhost:30001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"demo-user","password":"demo-pass"}'

# Test complete workflow
curl -X POST http://localhost:30000/api/workflow \
  -H "Content-Type: application/json" \
  -d '{"username":"demo-user","password":"demo-pass","productId":"product-1","quantity":1}'
```

## Monitoring and Observability

### OpenTelemetry Components

- **Agent (DaemonSet)**: Collects telemetry from each node
- **Gateway (Deployment)**: Processes traces with tail sampling
- **Cluster Collector**: Collects Kubernetes metadata

### Tail Sampling Policies

The tail sampling processor applies different sampling rates:

1. **Error Policy**: 100% sampling for all error traces
2. **Frontend**: 50% sampling rate
3. **Auth Service**: 75% sampling rate
4. **Orders Service**: 25% sampling rate
5. **Inventory Service**: 100% for errors, 10% for normal traces
6. **Default**: 20% for any other traces

### Monitoring Commands

```bash
# Check OpenTelemetry pods
kubectl get pods -l "app.kubernetes.io/name=opentelemetry-collector"

# View OpenTelemetry Gateway logs
kubectl logs -l "app.kubernetes.io/component=opentelemetry-gateway" -f

# Check application logs
kubectl logs -n kotak-demo -l app=frontend -f
kubectl logs -n kotak-demo -l app=backend-auth -f
kubectl logs -n kotak-demo -l app=backend-orders -f
kubectl logs -n kotak-demo -l app=backend-inventory -f
```

## Troubleshooting

### Common Issues

1. **Pods not starting**: Check if Docker images are built and loaded
   ```bash
   kind load docker-image kotak-frontend:latest --name kotak-tailsampling
   ```

2. **OpenTelemetry not receiving data**: Check endpoint configuration
   ```bash
   kubectl logs -l "app.kubernetes.io/name=opentelemetry-collector" --tail=50
   ```

3. **Coralogix connection issues**: Verify API key and domain
   ```bash
   kubectl get secret coralogix-keys -o yaml
   ```

### Cleanup

```bash
# Delete the Kind cluster
kind delete cluster --name kotak-tailsampling

# Remove Docker images
docker rmi kotak-frontend:latest kotak-backend-auth:latest kotak-backend-orders:latest kotak-backend-inventory:latest
```

## Architecture Details

### Service Communication Flow

1. **Frontend** receives user requests
2. **Frontend** calls **Auth Service** for authentication
3. **Frontend** calls **Orders Service** for order management
4. **Orders Service** calls **Inventory Service** for stock checks
5. **Inventory Service** manages product inventory

### OpenTelemetry Data Flow

1. Applications send telemetry to **OTel Agent** on each node
2. **OTel Agent** forwards traces to **OTel Gateway** using load balancing
3. **OTel Gateway** applies tail sampling policies
4. Sampled traces are sent to **Coralogix**

### Tail Sampling Decision Process

1. Gateway collects all spans for a trace
2. Waits for `decision_wait` period (15s)
3. Applies policies in order:
   - Error traces → 100% sampling
   - Service-specific policies → Different rates
   - Default policy → 20% sampling
4. Sends sampled traces to Coralogix

## Contributing

Feel free to modify the sampling policies in `otel/coralogix-values.yaml` to experiment with different configurations.

## License

This project is for demonstration purposes.

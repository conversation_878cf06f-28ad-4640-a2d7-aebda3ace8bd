global:
  domain: "coralogix.com"  # Will be overridden by setup script
  clusterName: "kotak-tailsampling-demo"
  defaultApplicationName: "kotak-demo"
  defaultSubsystemName: "microservices"
  logLevel: "info"
  collectionInterval: "30s"

# OpenTelemetry Agent (DaemonSet) - collects telemetry from nodes
opentelemetry-agent:
  enabled: true
  mode: daemonset
  presets:
    loadBalancing:
      enabled: true
      routingKey: "traceID"
      hostname: coralogix-opentelemetry-gateway
  
  config:
    service:
      pipelines:
        traces:
          exporters:
            - loadbalancing
        metrics:
          exporters:
            - loadbalancing
        logs:
          exporters:
            - loadbalancing

# OpenTelemetry Gateway (Deployment) - processes and forwards telemetry
opentelemetry-gateway:
  enabled: true
  replicaCount: 2
  
  config:
    processors:
      # Tail sampling processor with service-specific policies
      tail_sampling:
        decision_wait: 15s
        num_traces: 1000
        expected_new_traces_per_sec: 50
        policies:
          # Policy 1: Always sample error traces
          - name: errors-policy
            type: status_code
            status_code:
              status_codes: [ERROR]
          
          # Policy 2: Frontend service - 50% sampling
          - name: frontend-policy
            type: and
            and:
              and_sub_policy:
                - name: service-name-policy
                  type: string_attribute
                  string_attribute:
                    key: service.name
                    values: [frontend]
                - name: probabilistic-policy
                  type: probabilistic
                  probabilistic:
                    sampling_percentage: 50
          
          # Policy 3: Auth service - 75% sampling
          - name: auth-policy
            type: and
            and:
              and_sub_policy:
                - name: service-name-policy
                  type: string_attribute
                  string_attribute:
                    key: service.name
                    values: [backend-auth]
                - name: probabilistic-policy
                  type: probabilistic
                  probabilistic:
                    sampling_percentage: 75
          
          # Policy 4: Orders service - 25% sampling
          - name: orders-policy
            type: and
            and:
              and_sub_policy:
                - name: service-name-policy
                  type: string_attribute
                  string_attribute:
                    key: service.name
                    values: [backend-orders]
                - name: probabilistic-policy
                  type: probabilistic
                  probabilistic:
                    sampling_percentage: 25
          
          # Policy 5: Inventory service - 100% for errors, 10% for normal
          - name: inventory-error-policy
            type: and
            and:
              and_sub_policy:
                - name: service-name-policy
                  type: string_attribute
                  string_attribute:
                    key: service.name
                    values: [backend-inventory]
                - name: status-code-policy
                  type: status_code
                  status_code:
                    status_codes: [ERROR]
          
          - name: inventory-normal-policy
            type: and
            and:
              and_sub_policy:
                - name: service-name-policy
                  type: string_attribute
                  string_attribute:
                    key: service.name
                    values: [backend-inventory]
                - name: probabilistic-policy
                  type: probabilistic
                  probabilistic:
                    sampling_percentage: 10
          
          # Policy 6: Default policy for any other traces - 20% sampling
          - name: default-policy
            type: probabilistic
            probabilistic:
              sampling_percentage: 20
      
      # Resource processor to add additional attributes
      resource:
        attributes:
          - key: deployment.environment
            value: "demo"
            action: insert
          - key: service.version
            value: "1.0.0"
            action: insert
      
      # Batch processor for efficient data transmission
      batch:
        timeout: 1s
        send_batch_size: 1024
        send_batch_max_size: 2048

    service:
      pipelines:
        traces:
          processors:
            - resource
            - tail_sampling
            - batch
        metrics:
          processors:
            - resource
            - batch
        logs:
          processors:
            - resource
            - batch

# Cluster collector for Kubernetes metadata
opentelemetry-cluster-collector:
  enabled: true

# Windows agent (disabled for this demo)
opentelemetry-agent-windows:
  enabled: false

# eBPF agent (disabled for this demo)
coralogix-ebpf-agent:
  enabled: false

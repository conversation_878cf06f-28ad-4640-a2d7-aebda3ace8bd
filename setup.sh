#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to wait for pods to be ready
wait_for_pods() {
    local namespace=$1
    local label_selector=$2
    local timeout=${3:-300}
    
    print_status "Waiting for pods with label '$label_selector' in namespace '$namespace' to be ready..."
    kubectl wait --for=condition=ready pod -l "$label_selector" -n "$namespace" --timeout="${timeout}s"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    local missing_tools=()
    
    if ! command_exists kind; then
        missing_tools+=("kind")
    fi
    
    if ! command_exists kubectl; then
        missing_tools+=("kubectl")
    fi
    
    if ! command_exists helm; then
        missing_tools+=("helm")
    fi
    
    if ! command_exists docker; then
        missing_tools+=("docker")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_error "Please install them and run the script again."
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Check environment variables
    if [ -z "$CORALOGIX_API_KEY" ]; then
        print_error "CORALOGIX_API_KEY environment variable is not set."
        print_error "Please set it with: export CORALOGIX_API_KEY='your-api-key'"
        exit 1
    fi
    
    if [ -z "$CORALOGIX_DOMAIN" ]; then
        print_warning "CORALOGIX_DOMAIN not set, using default: coralogix.com"
        export CORALOGIX_DOMAIN="coralogix.com"
    fi
    
    print_success "All prerequisites met!"
}

# Create Kind cluster
create_cluster() {
    print_status "Creating Kind cluster with 2 nodes..."
    
    # Check if cluster already exists
    if kind get clusters | grep -q "kotak-tailsampling"; then
        print_warning "Cluster 'kotak-tailsampling' already exists. Deleting it first..."
        kind delete cluster --name kotak-tailsampling
    fi
    
    kind create cluster --config kind-cluster-config.yaml
    
    # Wait for cluster to be ready
    kubectl cluster-info --context kind-kotak-tailsampling
    
    print_success "Kind cluster created successfully!"
}

# Build and load Docker images
build_and_load_images() {
    print_status "Building and loading Docker images..."
    
    local apps=("frontend" "backend-auth" "backend-orders" "backend-inventory")
    
    for app in "${apps[@]}"; do
        print_status "Building $app image..."
        docker build -t "kotak-$app:latest" "apps/$app/"
        
        print_status "Loading $app image into Kind cluster..."
        kind load docker-image "kotak-$app:latest" --name kotak-tailsampling
    done
    
    print_success "All Docker images built and loaded!"
}

# Deploy applications
deploy_applications() {
    print_status "Deploying applications to Kubernetes..."
    
    # Create namespace
    kubectl apply -f k8s/namespace.yaml
    
    # Deploy all applications
    kubectl apply -f k8s/apps/
    
    # Wait for applications to be ready
    wait_for_pods "kotak-demo" "app=frontend"
    wait_for_pods "kotak-demo" "app=backend-auth"
    wait_for_pods "kotak-demo" "app=backend-orders"
    wait_for_pods "kotak-demo" "app=backend-inventory"
    
    print_success "Applications deployed successfully!"
}

# Install OpenTelemetry with Coralogix integration
install_opentelemetry() {
    print_status "Installing OpenTelemetry with Coralogix integration..."
    
    # Add Coralogix Helm repository
    helm repo add coralogix-charts-virtual https://cgx.jfrog.io/artifactory/coralogix-charts-virtual
    helm repo update
    
    # Create secret for Coralogix API key
    kubectl create secret generic coralogix-keys \
        --from-literal="PRIVATE_KEY=$CORALOGIX_API_KEY" \
        --namespace=default \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Install OpenTelemetry integration
    helm upgrade --install coralogix-opentelemetry \
        coralogix-charts-virtual/otel-integration \
        -f otel/coralogix-values.yaml \
        --set global.domain="$CORALOGIX_DOMAIN" \
        --wait --timeout=10m
    
    # Wait for OpenTelemetry components to be ready
    wait_for_pods "default" "app.kubernetes.io/name=opentelemetry-collector"
    
    print_success "OpenTelemetry installed successfully!"
}

# Validate setup
validate_setup() {
    print_status "Validating setup..."
    
    # Check cluster nodes
    print_status "Cluster nodes:"
    kubectl get nodes
    
    # Check application pods
    print_status "Application pods:"
    kubectl get pods -n kotak-demo
    
    # Check OpenTelemetry pods
    print_status "OpenTelemetry pods:"
    kubectl get pods -l "app.kubernetes.io/name=opentelemetry-collector"
    
    # Check services
    print_status "Services:"
    kubectl get svc -n kotak-demo
    
    print_success "Setup validation completed!"
}

# Main execution
main() {
    print_status "Starting Kotak Tail Sampling Demo Setup..."
    
    check_prerequisites
    create_cluster
    build_and_load_images
    deploy_applications
    install_opentelemetry
    validate_setup
    
    print_success "Setup completed successfully!"
    print_status "You can now test the applications using: ./scripts/test-endpoints.sh"
    print_status "Access the frontend at: http://localhost:30000"
}

# Run main function
main "$@"

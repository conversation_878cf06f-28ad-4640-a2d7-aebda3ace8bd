#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Base URLs
FRONTEND_URL="http://localhost:30000"
AUTH_URL="http://localhost:30001"
ORDERS_URL="http://localhost:30002"
INVENTORY_URL="http://localhost:30003"

# Function to test endpoint
test_endpoint() {
    local url=$1
    local method=${2:-GET}
    local data=${3:-""}
    local description=$4
    
    print_status "Testing: $description"
    echo "  URL: $url"
    echo "  Method: $method"
    
    if [ -n "$data" ]; then
        echo "  Data: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url" 2>/dev/null || echo -e "\n000")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url" 2>/dev/null || echo -e "\n000")
    fi
    
    # Extract response body and status code
    response_body=$(echo "$response" | head -n -1)
    status_code=$(echo "$response" | tail -n 1)
    
    if [ "$status_code" -ge 200 ] && [ "$status_code" -lt 300 ]; then
        print_success "✓ $description (Status: $status_code)"
        echo "  Response: $(echo "$response_body" | jq -c . 2>/dev/null || echo "$response_body")"
    elif [ "$status_code" -ge 400 ] && [ "$status_code" -lt 500 ]; then
        print_warning "⚠ $description (Status: $status_code)"
        echo "  Response: $(echo "$response_body" | jq -c . 2>/dev/null || echo "$response_body")"
    else
        print_error "✗ $description (Status: $status_code)"
        echo "  Response: $(echo "$response_body" | jq -c . 2>/dev/null || echo "$response_body")"
    fi
    
    echo ""
    sleep 1
}

# Function to generate load
generate_load() {
    local endpoint=$1
    local method=${2:-GET}
    local data=${3:-""}
    local count=${4:-10}
    local description=$5
    
    print_status "Generating load: $description ($count requests)"
    
    for i in $(seq 1 $count); do
        if [ -n "$data" ]; then
            curl -s -X "$method" \
                -H "Content-Type: application/json" \
                -d "$data" \
                "$endpoint" > /dev/null 2>&1 &
        else
            curl -s -X "$method" "$endpoint" > /dev/null 2>&1 &
        fi
        
        # Add some randomness to the timing
        sleep $(echo "scale=2; $RANDOM/32767 * 0.5" | bc -l 2>/dev/null || echo "0.1")
    done
    
    # Wait for all background jobs to complete
    wait
    print_success "✓ Load generation completed: $description"
    echo ""
}

# Check if services are accessible
check_services() {
    print_status "Checking if services are accessible..."
    
    # Check if kubectl is available and cluster is accessible
    if ! kubectl cluster-info >/dev/null 2>&1; then
        print_error "Kubernetes cluster is not accessible. Make sure Kind cluster is running."
        exit 1
    fi
    
    # Check if services are running
    print_status "Checking pod status..."
    kubectl get pods -n kotak-demo
    echo ""
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 5
}

# Main testing function
run_tests() {
    print_status "Starting endpoint testing..."
    echo ""
    
    # Health checks
    print_status "=== HEALTH CHECKS ==="
    test_endpoint "$FRONTEND_URL/health" "GET" "" "Frontend Health Check"
    test_endpoint "$AUTH_URL/health" "GET" "" "Auth Service Health Check"
    test_endpoint "$ORDERS_URL/health" "GET" "" "Orders Service Health Check"
    test_endpoint "$INVENTORY_URL/health" "GET" "" "Inventory Service Health Check"
    
    # Service info endpoints
    print_status "=== SERVICE INFO ==="
    test_endpoint "$FRONTEND_URL/" "GET" "" "Frontend Service Info"
    test_endpoint "$AUTH_URL/" "GET" "" "Auth Service Info"
    test_endpoint "$ORDERS_URL/" "GET" "" "Orders Service Info"
    test_endpoint "$INVENTORY_URL/" "GET" "" "Inventory Service Info"
    
    # Authentication tests
    print_status "=== AUTHENTICATION TESTS ==="
    test_endpoint "$AUTH_URL/auth/login" "POST" '{"username":"demo-user","password":"demo-pass"}' "Valid Login"
    test_endpoint "$AUTH_URL/auth/login" "POST" '{"username":"invalid","password":"invalid"}' "Invalid Login"
    test_endpoint "$AUTH_URL/auth/users" "GET" "" "Get Users List"
    
    # Inventory tests
    print_status "=== INVENTORY TESTS ==="
    test_endpoint "$INVENTORY_URL/inventory" "GET" "" "Get All Inventory"
    test_endpoint "$INVENTORY_URL/inventory/product-1" "GET" "" "Get Specific Product"
    test_endpoint "$INVENTORY_URL/inventory/nonexistent" "GET" "" "Get Nonexistent Product"
    
    # Orders tests
    print_status "=== ORDERS TESTS ==="
    test_endpoint "$ORDERS_URL/orders" "GET" "" "Get All Orders"
    test_endpoint "$ORDERS_URL/orders" "POST" '{"userId":"user-1","productId":"product-1","quantity":1}' "Create Order"
    
    # Frontend integration tests
    print_status "=== FRONTEND INTEGRATION TESTS ==="
    test_endpoint "$FRONTEND_URL/api/login" "POST" '{"username":"demo-user","password":"demo-pass"}' "Frontend Login"
    test_endpoint "$FRONTEND_URL/api/inventory" "GET" "" "Frontend Get Inventory"
    test_endpoint "$FRONTEND_URL/api/orders" "GET" "" "Frontend Get Orders"
    test_endpoint "$FRONTEND_URL/api/workflow" "POST" '{"username":"demo-user","password":"demo-pass","productId":"product-1","quantity":1}' "Frontend Complete Workflow"
    
    # Error simulation tests
    print_status "=== ERROR SIMULATION TESTS ==="
    test_endpoint "$AUTH_URL/auth/test-error" "GET" "" "Auth Service Error Test"
    test_endpoint "$ORDERS_URL/orders/test-error" "GET" "" "Orders Service Error Test"
    test_endpoint "$INVENTORY_URL/inventory/test-error" "GET" "" "Inventory Service Error Test"
}

# Load generation function
run_load_tests() {
    print_status "Starting load generation for telemetry data..."
    echo ""
    
    # Generate load on different services with different patterns
    generate_load "$FRONTEND_URL/health" "GET" "" 20 "Frontend Health Checks"
    generate_load "$FRONTEND_URL/api/login" "POST" '{"username":"demo-user","password":"demo-pass"}' 15 "Frontend Login Requests"
    generate_load "$AUTH_URL/auth/login" "POST" '{"username":"demo-user","password":"demo-pass"}' 25 "Auth Service Login Requests"
    generate_load "$ORDERS_URL/orders" "GET" "" 30 "Orders Service Get Requests"
    generate_load "$INVENTORY_URL/inventory" "GET" "" 35 "Inventory Service Get Requests"
    
    # Generate some error traces
    generate_load "$AUTH_URL/auth/test-error" "GET" "" 10 "Auth Service Error Generation"
    generate_load "$ORDERS_URL/orders/test-error" "GET" "" 8 "Orders Service Error Generation"
    generate_load "$INVENTORY_URL/inventory/test-error" "GET" "" 12 "Inventory Service Error Generation"
    
    # Generate complex workflow traces
    generate_load "$FRONTEND_URL/api/workflow" "POST" '{"username":"demo-user","password":"demo-pass","productId":"product-1","quantity":1}' 10 "Complete Workflow Traces"
    generate_load "$FRONTEND_URL/api/workflow" "POST" '{"username":"demo-user","password":"demo-pass","productId":"product-2","quantity":2}' 8 "Complete Workflow Traces (Product 2)"
    
    print_success "Load generation completed! Telemetry data should now be flowing to Coralogix."
}

# Function to show access information
show_access_info() {
    print_status "=== ACCESS INFORMATION ==="
    echo ""
    echo "Frontend Application: $FRONTEND_URL"
    echo "Auth Service: $AUTH_URL"
    echo "Orders Service: $ORDERS_URL"
    echo "Inventory Service: $INVENTORY_URL"
    echo ""
    echo "You can access the frontend web interface at: $FRONTEND_URL"
    echo ""
    print_status "Sample API calls:"
    echo "curl $FRONTEND_URL/health"
    echo "curl -X POST $FRONTEND_URL/api/login -H 'Content-Type: application/json' -d '{\"username\":\"demo-user\",\"password\":\"demo-pass\"}'"
    echo "curl -X POST $FRONTEND_URL/api/workflow -H 'Content-Type: application/json' -d '{\"username\":\"demo-user\",\"password\":\"demo-pass\",\"productId\":\"product-1\",\"quantity\":1}'"
    echo ""
}

# Main execution
main() {
    print_status "Kotak Tail Sampling Demo - Endpoint Testing"
    echo ""
    
    check_services
    
    case "${1:-test}" in
        "test")
            run_tests
            ;;
        "load")
            run_load_tests
            ;;
        "both")
            run_tests
            echo ""
            print_status "Waiting 10 seconds before starting load generation..."
            sleep 10
            run_load_tests
            ;;
        "info")
            show_access_info
            ;;
        *)
            echo "Usage: $0 [test|load|both|info]"
            echo "  test - Run endpoint tests"
            echo "  load - Generate load for telemetry"
            echo "  both - Run tests then generate load"
            echo "  info - Show access information"
            exit 1
            ;;
    esac
    
    echo ""
    show_access_info
    
    print_success "Testing completed! Check your Coralogix dashboard for telemetry data."
    print_status "Tail sampling should be applied based on the configured policies:"
    echo "  - Frontend: 50% sampling"
    echo "  - Auth: 75% sampling"
    echo "  - Orders: 25% sampling"
    echo "  - Inventory: 100% for errors, 10% for normal traces"
}

# Run main function
main "$@"

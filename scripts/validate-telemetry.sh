#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check OpenTelemetry components
check_otel_components() {
    print_status "Checking OpenTelemetry components..."
    
    # Check if OpenTelemetry pods are running
    print_status "OpenTelemetry Agent pods:"
    kubectl get pods -l "app.kubernetes.io/name=opentelemetry-collector" -o wide
    
    print_status "OpenTelemetry Gateway pods:"
    kubectl get pods -l "app.kubernetes.io/component=opentelemetry-gateway" -o wide
    
    # Check pod status
    local agent_ready=$(kubectl get pods -l "app.kubernetes.io/name=opentelemetry-collector" -o jsonpath='{.items[*].status.conditions[?(@.type=="Ready")].status}' | grep -c "True" || echo "0")
    local gateway_ready=$(kubectl get pods -l "app.kubernetes.io/component=opentelemetry-gateway" -o jsonpath='{.items[*].status.conditions[?(@.type=="Ready")].status}' | grep -c "True" || echo "0")
    
    if [ "$agent_ready" -gt 0 ]; then
        print_success "✓ OpenTelemetry Agent pods are ready ($agent_ready pods)"
    else
        print_error "✗ No OpenTelemetry Agent pods are ready"
    fi
    
    if [ "$gateway_ready" -gt 0 ]; then
        print_success "✓ OpenTelemetry Gateway pods are ready ($gateway_ready pods)"
    else
        print_error "✗ No OpenTelemetry Gateway pods are ready"
    fi
    
    echo ""
}

# Check application pods
check_application_pods() {
    print_status "Checking application pods..."
    
    local apps=("frontend" "backend-auth" "backend-orders" "backend-inventory")
    
    for app in "${apps[@]}"; do
        local ready=$(kubectl get pods -n kotak-demo -l "app=$app" -o jsonpath='{.items[*].status.conditions[?(@.type=="Ready")].status}' | grep -c "True" || echo "0")
        local total=$(kubectl get pods -n kotak-demo -l "app=$app" --no-headers | wc -l)
        
        if [ "$ready" -gt 0 ]; then
            print_success "✓ $app pods are ready ($ready/$total pods)"
        else
            print_error "✗ No $app pods are ready (0/$total pods)"
        fi
    done
    
    echo ""
}

# Check services
check_services() {
    print_status "Checking services..."
    
    kubectl get svc -n kotak-demo
    
    # Check if NodePort services are accessible
    local services=("frontend:30000" "backend-auth-nodeport:30001" "backend-orders-nodeport:30002" "backend-inventory-nodeport:30003")
    
    for service_port in "${services[@]}"; do
        local service=$(echo "$service_port" | cut -d: -f1)
        local port=$(echo "$service_port" | cut -d: -f2)
        
        if curl -s --max-time 5 "http://localhost:$port/health" > /dev/null 2>&1; then
            print_success "✓ $service is accessible on port $port"
        else
            print_warning "⚠ $service is not accessible on port $port"
        fi
    done
    
    echo ""
}

# Check OpenTelemetry configuration
check_otel_config() {
    print_status "Checking OpenTelemetry configuration..."
    
    # Get the OpenTelemetry gateway configuration
    local gateway_pod=$(kubectl get pods -l "app.kubernetes.io/component=opentelemetry-gateway" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$gateway_pod" ]; then
        print_status "Found OpenTelemetry Gateway pod: $gateway_pod"
        
        # Check if tail sampling processor is configured
        if kubectl logs "$gateway_pod" --tail=50 | grep -q "tail_sampling"; then
            print_success "✓ Tail sampling processor is configured"
        else
            print_warning "⚠ Tail sampling processor configuration not found in logs"
        fi
        
        # Check for any errors in the logs
        local error_count=$(kubectl logs "$gateway_pod" --tail=100 | grep -i error | wc -l)
        if [ "$error_count" -eq 0 ]; then
            print_success "✓ No errors found in OpenTelemetry Gateway logs"
        else
            print_warning "⚠ Found $error_count error messages in OpenTelemetry Gateway logs"
        fi
    else
        print_error "✗ No OpenTelemetry Gateway pod found"
    fi
    
    echo ""
}

# Check telemetry data flow
check_telemetry_flow() {
    print_status "Checking telemetry data flow..."
    
    # Generate some test traffic
    print_status "Generating test traffic to create telemetry data..."
    
    local endpoints=(
        "http://localhost:30000/health"
        "http://localhost:30001/health"
        "http://localhost:30002/health"
        "http://localhost:30003/health"
    )
    
    for endpoint in "${endpoints[@]}"; do
        curl -s "$endpoint" > /dev/null 2>&1 &
    done
    
    # Wait for requests to complete
    wait
    
    # Check OpenTelemetry logs for trace processing
    local gateway_pod=$(kubectl get pods -l "app.kubernetes.io/component=opentelemetry-gateway" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$gateway_pod" ]; then
        print_status "Checking recent OpenTelemetry Gateway logs for trace processing..."
        
        # Look for trace processing indicators in logs
        local recent_logs=$(kubectl logs "$gateway_pod" --tail=20 --since=30s 2>/dev/null || echo "")
        
        if echo "$recent_logs" | grep -q -E "(trace|span|export)"; then
            print_success "✓ Telemetry data processing detected in logs"
        else
            print_warning "⚠ No recent telemetry processing detected in logs"
        fi
        
        # Show recent logs
        print_status "Recent OpenTelemetry Gateway logs:"
        echo "$recent_logs" | tail -10
    fi
    
    echo ""
}

# Check Coralogix connectivity
check_coralogix_connectivity() {
    print_status "Checking Coralogix connectivity..."
    
    # Check if Coralogix secret exists
    if kubectl get secret coralogix-keys >/dev/null 2>&1; then
        print_success "✓ Coralogix API key secret exists"
    else
        print_error "✗ Coralogix API key secret not found"
        print_error "  Please ensure CORALOGIX_API_KEY environment variable is set and run setup.sh"
    fi
    
    # Check OpenTelemetry exporter configuration
    local gateway_pod=$(kubectl get pods -l "app.kubernetes.io/component=opentelemetry-gateway" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -n "$gateway_pod" ]; then
        # Check for Coralogix-related configuration or logs
        if kubectl logs "$gateway_pod" --tail=50 | grep -q -i coralogix; then
            print_success "✓ Coralogix configuration detected in OpenTelemetry logs"
        else
            print_warning "⚠ No Coralogix-specific configuration found in logs"
        fi
        
        # Check for export errors
        local export_errors=$(kubectl logs "$gateway_pod" --tail=100 | grep -i "export.*error" | wc -l)
        if [ "$export_errors" -eq 0 ]; then
            print_success "✓ No export errors found in logs"
        else
            print_warning "⚠ Found $export_errors export error messages in logs"
        fi
    fi
    
    echo ""
}

# Show tail sampling configuration
show_tail_sampling_config() {
    print_status "Tail Sampling Configuration:"
    echo ""
    echo "The following tail sampling policies are configured:"
    echo ""
    echo "1. Error Policy:"
    echo "   - Samples: 100% of traces with ERROR status"
    echo "   - Applies to: All services"
    echo ""
    echo "2. Frontend Policy:"
    echo "   - Samples: 50% of traces from 'frontend' service"
    echo "   - Service: frontend"
    echo ""
    echo "3. Auth Policy:"
    echo "   - Samples: 75% of traces from 'backend-auth' service"
    echo "   - Service: backend-auth"
    echo ""
    echo "4. Orders Policy:"
    echo "   - Samples: 25% of traces from 'backend-orders' service"
    echo "   - Service: backend-orders"
    echo ""
    echo "5. Inventory Error Policy:"
    echo "   - Samples: 100% of ERROR traces from 'backend-inventory' service"
    echo "   - Service: backend-inventory"
    echo ""
    echo "6. Inventory Normal Policy:"
    echo "   - Samples: 10% of normal traces from 'backend-inventory' service"
    echo "   - Service: backend-inventory"
    echo ""
    echo "7. Default Policy:"
    echo "   - Samples: 20% of all other traces"
    echo "   - Applies to: Any traces not matched by above policies"
    echo ""
}

# Show monitoring recommendations
show_monitoring_recommendations() {
    print_status "Monitoring Recommendations:"
    echo ""
    echo "1. Check your Coralogix dashboard for incoming telemetry data"
    echo "2. Look for traces from the following services:"
    echo "   - frontend"
    echo "   - backend-auth"
    echo "   - backend-orders"
    echo "   - backend-inventory"
    echo ""
    echo "3. Verify tail sampling is working by observing:"
    echo "   - Different sampling rates for different services"
    echo "   - 100% sampling of error traces"
    echo "   - Reduced overall trace volume compared to head sampling"
    echo ""
    echo "4. Generate more test data using:"
    echo "   ./scripts/test-endpoints.sh load"
    echo ""
    echo "5. Monitor OpenTelemetry components:"
    echo "   kubectl logs -l app.kubernetes.io/component=opentelemetry-gateway -f"
    echo ""
}

# Main validation function
main() {
    print_status "Kotak Tail Sampling Demo - Telemetry Validation"
    echo ""
    
    # Check prerequisites
    if ! command_exists kubectl; then
        print_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    if ! kubectl cluster-info >/dev/null 2>&1; then
        print_error "Kubernetes cluster is not accessible"
        exit 1
    fi
    
    # Run validation checks
    check_otel_components
    check_application_pods
    check_services
    check_otel_config
    check_telemetry_flow
    check_coralogix_connectivity
    
    # Show configuration and recommendations
    show_tail_sampling_config
    show_monitoring_recommendations
    
    print_success "Validation completed!"
    print_status "If all checks passed, your tail sampling setup should be working correctly."
    print_status "Check your Coralogix dashboard to verify telemetry data is being received."
}

# Run main function
main "$@"

const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { trace } = require('@opentelemetry/api');

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = process.env.JWT_SECRET || 'demo-secret-key';

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// Get tracer
const tracer = trace.getTracer('backend-auth-service', '1.0.0');

// Mock user database
const users = [
  {
    id: 'user-1',
    username: 'demo-user',
    password: bcrypt.hashSync('demo-pass', 10),
    email: '<EMAIL>',
    role: 'user'
  },
  {
    id: 'user-2',
    username: 'admin',
    password: bcrypt.hashSync('admin-pass', 10),
    email: '<EMAIL>',
    role: 'admin'
  },
  {
    id: 'user-3',
    username: 'test-user',
    password: bcrypt.hashSync('test-pass', 10),
    email: '<EMAIL>',
    role: 'user'
  }
];

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    service: 'backend-auth',
    timestamp: new Date().toISOString()
  });
});

// Service info endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Authentication Service',
    service: 'backend-auth',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      login: '/auth/login',
      verify: '/auth/verify',
      users: '/auth/users'
    }
  });
});

// Login endpoint
app.post('/auth/login', async (req, res) => {
  const span = tracer.startSpan('auth.login');
  
  try {
    span.setAttributes({
      'http.method': 'POST',
      'http.route': '/auth/login',
      'service.name': 'backend-auth'
    });

    const { username, password } = req.body;
    
    if (!username || !password) {
      span.recordException(new Error('Missing credentials'));
      span.setStatus({ code: 2, message: 'Missing credentials' });
      return res.status(400).json({ error: 'Username and password required' });
    }

    // Simulate database lookup
    const dbSpan = tracer.startSpan('auth.database.lookup', { parent: span });
    let user;
    try {
      // Simulate some processing time
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
      user = users.find(u => u.username === username);
      dbSpan.setAttributes({
        'db.operation': 'select',
        'db.table': 'users',
        'user.found': !!user
      });
    } catch (error) {
      dbSpan.recordException(error);
      dbSpan.setStatus({ code: 2, message: error.message });
      throw error;
    } finally {
      dbSpan.end();
    }

    if (!user) {
      span.setAttributes({
        'auth.username': username,
        'auth.success': false,
        'auth.failure_reason': 'user_not_found'
      });
      span.setStatus({ code: 2, message: 'User not found' });
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Verify password
    const passwordSpan = tracer.startSpan('auth.password.verify', { parent: span });
    let isValidPassword;
    try {
      isValidPassword = await bcrypt.compare(password, user.password);
      passwordSpan.setAttributes({
        'auth.password_valid': isValidPassword
      });
    } catch (error) {
      passwordSpan.recordException(error);
      passwordSpan.setStatus({ code: 2, message: error.message });
      throw error;
    } finally {
      passwordSpan.end();
    }

    if (!isValidPassword) {
      span.setAttributes({
        'auth.username': username,
        'auth.success': false,
        'auth.failure_reason': 'invalid_password'
      });
      span.setStatus({ code: 2, message: 'Invalid password' });
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate JWT token
    const tokenSpan = tracer.startSpan('auth.token.generate', { parent: span });
    let token;
    try {
      token = jwt.sign(
        { 
          userId: user.id, 
          username: user.username, 
          role: user.role 
        },
        JWT_SECRET,
        { expiresIn: '1h' }
      );
      tokenSpan.setAttributes({
        'jwt.expires_in': '1h',
        'user.role': user.role
      });
    } catch (error) {
      tokenSpan.recordException(error);
      tokenSpan.setStatus({ code: 2, message: error.message });
      throw error;
    } finally {
      tokenSpan.end();
    }

    span.setAttributes({
      'auth.username': username,
      'auth.success': true,
      'user.id': user.id,
      'user.role': user.role
    });

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      }
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Login error:', error.message);
    res.status(500).json({ 
      error: 'Authentication failed',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Token verification endpoint
app.post('/auth/verify', (req, res) => {
  const span = tracer.startSpan('auth.verify_token');
  
  try {
    span.setAttributes({
      'http.method': 'POST',
      'http.route': '/auth/verify',
      'service.name': 'backend-auth'
    });

    const { token } = req.body;
    
    if (!token) {
      span.recordException(new Error('Missing token'));
      span.setStatus({ code: 2, message: 'Missing token' });
      return res.status(400).json({ error: 'Token required' });
    }

    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      
      span.setAttributes({
        'jwt.valid': true,
        'user.id': decoded.userId,
        'user.role': decoded.role
      });

      res.json({
        valid: true,
        user: {
          id: decoded.userId,
          username: decoded.username,
          role: decoded.role
        }
      });

    } catch (jwtError) {
      span.recordException(jwtError);
      span.setAttributes({
        'jwt.valid': false,
        'jwt.error': jwtError.message
      });
      span.setStatus({ code: 2, message: jwtError.message });
      
      res.status(401).json({ 
        valid: false, 
        error: 'Invalid token' 
      });
    }

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Token verification error:', error.message);
    res.status(500).json({ 
      error: 'Token verification failed',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Get users endpoint (for demo purposes)
app.get('/auth/users', (req, res) => {
  const span = tracer.startSpan('auth.get_users');
  
  try {
    span.setAttributes({
      'http.method': 'GET',
      'http.route': '/auth/users',
      'service.name': 'backend-auth'
    });

    // Return users without passwords
    const safeUsers = users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    }));

    span.setAttributes({
      'users.count': safeUsers.length
    });

    res.json({
      users: safeUsers,
      count: safeUsers.length
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Get users error:', error.message);
    res.status(500).json({ 
      error: 'Failed to fetch users',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Simulate occasional errors for testing
app.get('/auth/test-error', (req, res) => {
  const span = tracer.startSpan('auth.test_error');
  
  try {
    span.setAttributes({
      'http.method': 'GET',
      'http.route': '/auth/test-error',
      'service.name': 'backend-auth',
      'test.error_simulation': true
    });

    // 30% chance of error
    if (Math.random() < 0.3) {
      const error = new Error('Simulated authentication service error');
      span.recordException(error);
      span.setStatus({ code: 2, message: error.message });
      throw error;
    }

    span.setAttributes({
      'test.result': 'success'
    });

    res.json({
      message: 'Test endpoint - no error this time',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Test error:', error.message);
    res.status(500).json({ 
      error: 'Simulated error occurred',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Auth service running on port ${PORT}`);
  console.log(`JWT Secret configured: ${JWT_SECRET ? 'Yes' : 'No'}`);
});

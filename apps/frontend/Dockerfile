FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Create public directory for static files
RUN mkdir -p public

# Create a simple HTML file
RUN echo '<!DOCTYPE html>
<html>
<head>
    <title>Kotak Tail Sampling Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        .result { background: #e8f5e8; padding: 10px; margin: 10px 0; border-radius: 5px; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Kotak <PERSON>l Sampling Demo - Frontend</h1>
        <p>This is the frontend service of the distributed application demo.</p>
        
        <h2>Available Endpoints:</h2>
        <div class="endpoint">
            <strong>GET /health</strong> - Health check
            <button onclick="testEndpoint(\"/health\", \"GET\")">Test</button>
        </div>
        <div class="endpoint">
            <strong>POST /api/login</strong> - User authentication
            <button onclick="testLogin()">Test Login</button>
        </div>
        <div class="endpoint">
            <strong>GET /api/orders</strong> - Get orders
            <button onclick="testEndpoint(\"/api/orders\", \"GET\")">Test</button>
        </div>
        <div class="endpoint">
            <strong>GET /api/inventory</strong> - Get inventory
            <button onclick="testEndpoint(\"/api/inventory\", \"GET\")">Test</button>
        </div>
        <div class="endpoint">
            <strong>POST /api/workflow</strong> - Complete workflow
            <button onclick="testWorkflow()">Test Workflow</button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        async function testEndpoint(path, method, body = null) {
            const resultDiv = document.getElementById("result");
            resultDiv.style.display = "block";
            resultDiv.textContent = "Loading...";
            
            try {
                const options = {
                    method: method,
                    headers: { "Content-Type": "application/json" }
                };
                if (body) options.body = JSON.stringify(body);
                
                const response = await fetch(path, options);
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = "Error: " + error.message;
            }
        }
        
        function testLogin() {
            testEndpoint("/api/login", "POST", {
                username: "demo-user",
                password: "demo-pass"
            });
        }
        
        function testWorkflow() {
            testEndpoint("/api/workflow", "POST", {
                username: "demo-user",
                password: "demo-pass",
                productId: "product-1",
                quantity: 2
            });
        }
    </script>
</body>
</html>' > public/index.html

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
CMD ["npm", "start"]

{"name": "kotak-frontend", "version": "1.0.0", "description": "Frontend service for Kotak tail sampling demo", "main": "server.js", "scripts": {"start": "node -r ./tracing.js server.js", "dev": "nodemon -r ./tracing.js server.js"}, "dependencies": {"express": "^4.18.2", "axios": "^1.6.0", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "@opentelemetry/api": "^1.7.0", "@opentelemetry/sdk-node": "^0.45.0", "@opentelemetry/auto-instrumentations-node": "^0.40.0", "@opentelemetry/exporter-otlp-grpc": "^0.45.0", "@opentelemetry/resources": "^1.18.0", "@opentelemetry/semantic-conventions": "^1.18.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}
const express = require('express');
const axios = require('axios');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { trace, context } = require('@opentelemetry/api');

const app = express();
const PORT = process.env.PORT || 3000;

// Service endpoints
const AUTH_SERVICE = process.env.AUTH_SERVICE_URL || 'http://backend-auth:3001';
const ORDERS_SERVICE = process.env.ORDERS_SERVICE_URL || 'http://backend-orders:3002';
const INVENTORY_SERVICE = process.env.INVENTORY_SERVICE_URL || 'http://backend-inventory:3003';

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.static('public'));

// Get tracer
const tracer = trace.getTracer('frontend-service', '1.0.0');

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    service: 'frontend',
    timestamp: new Date().toISOString()
  });
});

// Home page
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to Kotak Tail Sampling Demo',
    service: 'frontend',
    endpoints: {
      health: '/health',
      login: '/api/login',
      orders: '/api/orders',
      inventory: '/api/inventory',
      workflow: '/api/workflow'
    }
  });
});

// Login endpoint - calls auth service
app.post('/api/login', async (req, res) => {
  const span = tracer.startSpan('frontend.login');
  
  try {
    span.setAttributes({
      'http.method': 'POST',
      'http.route': '/api/login',
      'service.name': 'frontend'
    });

    const { username, password } = req.body;
    
    if (!username || !password) {
      span.recordException(new Error('Missing username or password'));
      span.setStatus({ code: 2, message: 'Missing credentials' });
      return res.status(400).json({ error: 'Username and password required' });
    }

    // Call auth service
    const authResponse = await axios.post(`${AUTH_SERVICE}/auth/login`, {
      username,
      password
    });

    span.setAttributes({
      'auth.username': username,
      'auth.success': true
    });

    res.json({
      message: 'Login successful',
      token: authResponse.data.token,
      user: authResponse.data.user
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Login error:', error.message);
    res.status(500).json({ 
      error: 'Login failed',
      details: error.response?.data || error.message
    });
  } finally {
    span.end();
  }
});

// Orders endpoint - calls orders service
app.get('/api/orders', async (req, res) => {
  const span = tracer.startSpan('frontend.get_orders');
  
  try {
    span.setAttributes({
      'http.method': 'GET',
      'http.route': '/api/orders',
      'service.name': 'frontend'
    });

    const ordersResponse = await axios.get(`${ORDERS_SERVICE}/orders`);
    
    span.setAttributes({
      'orders.count': ordersResponse.data.orders?.length || 0
    });

    res.json(ordersResponse.data);

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Orders error:', error.message);
    res.status(500).json({ 
      error: 'Failed to fetch orders',
      details: error.response?.data || error.message
    });
  } finally {
    span.end();
  }
});

// Inventory endpoint - calls inventory service
app.get('/api/inventory', async (req, res) => {
  const span = tracer.startSpan('frontend.get_inventory');
  
  try {
    span.setAttributes({
      'http.method': 'GET',
      'http.route': '/api/inventory',
      'service.name': 'frontend'
    });

    const inventoryResponse = await axios.get(`${INVENTORY_SERVICE}/inventory`);
    
    span.setAttributes({
      'inventory.items_count': inventoryResponse.data.items?.length || 0
    });

    res.json(inventoryResponse.data);

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Inventory error:', error.message);
    res.status(500).json({ 
      error: 'Failed to fetch inventory',
      details: error.response?.data || error.message
    });
  } finally {
    span.end();
  }
});

// Complete workflow endpoint - calls all services
app.post('/api/workflow', async (req, res) => {
  const span = tracer.startSpan('frontend.complete_workflow');
  
  try {
    span.setAttributes({
      'http.method': 'POST',
      'http.route': '/api/workflow',
      'service.name': 'frontend',
      'workflow.type': 'complete'
    });

    const { username, password, productId, quantity } = req.body;

    // Step 1: Authenticate
    const authSpan = tracer.startSpan('frontend.workflow.auth', { parent: span });
    let authResult;
    try {
      const authResponse = await axios.post(`${AUTH_SERVICE}/auth/login`, {
        username: username || 'demo-user',
        password: password || 'demo-pass'
      });
      authResult = authResponse.data;
      authSpan.setAttributes({ 'auth.success': true });
    } catch (error) {
      authSpan.recordException(error);
      authSpan.setStatus({ code: 2, message: error.message });
      throw new Error(`Authentication failed: ${error.message}`);
    } finally {
      authSpan.end();
    }

    // Step 2: Check inventory
    const inventorySpan = tracer.startSpan('frontend.workflow.inventory', { parent: span });
    let inventoryResult;
    try {
      const inventoryResponse = await axios.get(`${INVENTORY_SERVICE}/inventory/${productId || 'product-1'}`);
      inventoryResult = inventoryResponse.data;
      inventorySpan.setAttributes({ 
        'inventory.product_id': productId || 'product-1',
        'inventory.available': inventoryResult.available
      });
    } catch (error) {
      inventorySpan.recordException(error);
      inventorySpan.setStatus({ code: 2, message: error.message });
      throw new Error(`Inventory check failed: ${error.message}`);
    } finally {
      inventorySpan.end();
    }

    // Step 3: Create order
    const orderSpan = tracer.startSpan('frontend.workflow.order', { parent: span });
    let orderResult;
    try {
      const orderResponse = await axios.post(`${ORDERS_SERVICE}/orders`, {
        userId: authResult.user?.id || 'demo-user-id',
        productId: productId || 'product-1',
        quantity: quantity || 1
      });
      orderResult = orderResponse.data;
      orderSpan.setAttributes({ 
        'order.id': orderResult.orderId,
        'order.status': orderResult.status
      });
    } catch (error) {
      orderSpan.recordException(error);
      orderSpan.setStatus({ code: 2, message: error.message });
      throw new Error(`Order creation failed: ${error.message}`);
    } finally {
      orderSpan.end();
    }

    span.setAttributes({
      'workflow.success': true,
      'workflow.steps_completed': 3
    });

    res.json({
      message: 'Workflow completed successfully',
      results: {
        auth: authResult,
        inventory: inventoryResult,
        order: orderResult
      }
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Workflow error:', error.message);
    res.status(500).json({ 
      error: 'Workflow failed',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Frontend service running on port ${PORT}`);
  console.log(`Service endpoints configured:`);
  console.log(`- Auth Service: ${AUTH_SERVICE}`);
  console.log(`- Orders Service: ${ORDERS_SERVICE}`);
  console.log(`- Inventory Service: ${INVENTORY_SERVICE}`);
});

<!DOCTYPE html>
<html>
<head>
    <title>Kotak <PERSON>l Sampling Demo</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .endpoint { 
            background: #f8f9fa; 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 8px; 
            border-left: 4px solid #007cba; 
        }
        button { 
            background: #007cba; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px; 
            font-size: 14px; 
        }
        button:hover { 
            background: #005a87; 
        }
        .result { 
            background: #e8f5e8; 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 8px; 
            white-space: pre-wrap; 
            font-family: monospace; 
            border: 1px solid #d4edda; 
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .status {
            background: #d1ecf1;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Kotak Tail Sampling Demo</h1>
            <p>Frontend Service - OpenTelemetry Distributed Tracing</p>
        </div>
        
        <div class="status">
            <strong>Status:</strong> Frontend service is running and instrumented with OpenTelemetry!
        </div>
        
        <h2>🔗 Available API Endpoints:</h2>
        
        <div class="endpoint">
            <strong>GET /health</strong> - Health check endpoint
            <br><small>Check if the service is running</small>
            <br><button onclick="testEndpoint('/health', 'GET')">Test Health</button>
        </div>
        
        <div class="endpoint">
            <strong>POST /api/login</strong> - User authentication
            <br><small>Authenticate with the auth service</small>
            <br><button onclick="testLogin()">Test Login</button>
        </div>
        
        <div class="endpoint">
            <strong>GET /api/orders</strong> - Get orders
            <br><small>Fetch orders from the orders service</small>
            <br><button onclick="testEndpoint('/api/orders', 'GET')">Test Orders</button>
        </div>
        
        <div class="endpoint">
            <strong>GET /api/inventory</strong> - Get inventory
            <br><small>Fetch inventory from the inventory service</small>
            <br><button onclick="testEndpoint('/api/inventory', 'GET')">Test Inventory</button>
        </div>
        
        <div class="endpoint">
            <strong>POST /api/workflow</strong> - Complete workflow
            <br><small>Execute a complete workflow across all services</small>
            <br><button onclick="testWorkflow()">Test Complete Workflow</button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <h2>📊 Tail Sampling Information:</h2>
        <div class="status">
            <p><strong>This demo showcases OpenTelemetry tail sampling with different rates:</strong></p>
            <ul>
                <li>Frontend: 50% sampling rate</li>
                <li>Auth Service: 75% sampling rate</li>
                <li>Orders Service: 25% sampling rate</li>
                <li>Inventory Service: 100% for errors, 10% for normal traces</li>
            </ul>
            <p>All error traces are sampled at 100% regardless of service!</p>
        </div>
    </div>

    <script>
        async function testEndpoint(path, method, body = null) {
            const resultDiv = document.getElementById("result");
            resultDiv.style.display = "block";
            resultDiv.textContent = "🔄 Loading...";
            
            try {
                const options = {
                    method: method,
                    headers: { "Content-Type": "application/json" }
                };
                if (body) options.body = JSON.stringify(body);
                
                const response = await fetch(path, options);
                const data = await response.json();
                
                resultDiv.innerHTML = `<strong>✅ Response (${response.status}):</strong>\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.innerHTML = `<strong>❌ Error:</strong>\n${error.message}`;
            }
        }
        
        function testLogin() {
            testEndpoint("/api/login", "POST", {
                username: "demo-user",
                password: "demo-pass"
            });
        }
        
        function testWorkflow() {
            testEndpoint("/api/workflow", "POST", {
                username: "demo-user",
                password: "demo-pass",
                productId: "product-1",
                quantity: 2
            });
        }
        
        // Auto-test health endpoint on page load
        window.onload = function() {
            setTimeout(() => {
                testEndpoint('/health', 'GET');
            }, 1000);
        };
    </script>
</body>
</html>

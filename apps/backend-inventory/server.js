const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { trace } = require('@opentelemetry/api');

const app = express();
const PORT = process.env.PORT || 3003;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// Get tracer
const tracer = trace.getTracer('backend-inventory-service', '1.0.0');

// Mock inventory database
let inventory = [
  {
    id: 'product-1',
    name: 'Laptop Computer',
    description: 'High-performance laptop',
    price: 999.99,
    stock: 50,
    available: true,
    category: 'electronics',
    reserved: 0
  },
  {
    id: 'product-2',
    name: 'Wireless Mouse',
    description: 'Ergonomic wireless mouse',
    price: 29.99,
    stock: 100,
    available: true,
    category: 'accessories',
    reserved: 0
  },
  {
    id: 'product-3',
    name: 'Mechanical Keyboard',
    description: 'RGB mechanical keyboard',
    price: 149.99,
    stock: 25,
    available: true,
    category: 'accessories',
    reserved: 0
  },
  {
    id: 'product-4',
    name: 'Monitor',
    description: '4K Ultra HD monitor',
    price: 399.99,
    stock: 0,
    available: false,
    category: 'electronics',
    reserved: 0
  }
];

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    service: 'backend-inventory',
    timestamp: new Date().toISOString()
  });
});

// Service info endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Inventory Service',
    service: 'backend-inventory',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      inventory: '/inventory',
      getProduct: '/inventory/:id',
      reserve: 'POST /inventory/:id/reserve',
      release: 'POST /inventory/:id/release'
    }
  });
});

// Get all inventory items
app.get('/inventory', (req, res) => {
  const span = tracer.startSpan('inventory.get_all');
  
  try {
    span.setAttributes({
      'http.method': 'GET',
      'http.route': '/inventory',
      'service.name': 'backend-inventory'
    });

    // Simulate database query
    const dbSpan = tracer.startSpan('inventory.database.select_all', { parent: span });
    try {
      // Simulate some processing time
      setTimeout(() => {}, Math.random() * 50 + 25);
      
      dbSpan.setAttributes({
        'db.operation': 'select',
        'db.table': 'inventory',
        'db.rows_affected': inventory.length
      });
    } catch (error) {
      dbSpan.recordException(error);
      dbSpan.setStatus({ code: 2, message: error.message });
      throw error;
    } finally {
      dbSpan.end();
    }

    // Calculate totals
    const totalItems = inventory.length;
    const availableItems = inventory.filter(item => item.available).length;
    const totalStock = inventory.reduce((sum, item) => sum + item.stock, 0);

    span.setAttributes({
      'inventory.total_items': totalItems,
      'inventory.available_items': availableItems,
      'inventory.total_stock': totalStock
    });

    res.json({
      items: inventory,
      summary: {
        totalItems,
        availableItems,
        totalStock,
        outOfStock: totalItems - availableItems
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Get inventory error:', error.message);
    res.status(500).json({ 
      error: 'Failed to fetch inventory',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Get specific product
app.get('/inventory/:id', (req, res) => {
  const span = tracer.startSpan('inventory.get_by_id');
  
  try {
    span.setAttributes({
      'http.method': 'GET',
      'http.route': '/inventory/:id',
      'service.name': 'backend-inventory',
      'product.id': req.params.id
    });

    const productId = req.params.id;
    
    // Simulate occasional errors for this service (20% chance)
    if (Math.random() < 0.2) {
      const error = new Error('Simulated inventory service error - database connection failed');
      span.recordException(error);
      span.setStatus({ code: 2, message: error.message });
      throw error;
    }

    const product = inventory.find(item => item.id === productId);

    if (!product) {
      span.setAttributes({
        'product.found': false
      });
      span.setStatus({ code: 2, message: 'Product not found' });
      return res.status(404).json({ error: 'Product not found' });
    }

    span.setAttributes({
      'product.found': true,
      'product.name': product.name,
      'product.stock': product.stock,
      'product.available': product.available,
      'product.price': product.price
    });

    res.json(product);

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Get product error:', error.message);
    res.status(500).json({ 
      error: 'Failed to fetch product',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Reserve inventory
app.post('/inventory/:id/reserve', (req, res) => {
  const span = tracer.startSpan('inventory.reserve');
  
  try {
    span.setAttributes({
      'http.method': 'POST',
      'http.route': '/inventory/:id/reserve',
      'service.name': 'backend-inventory',
      'product.id': req.params.id
    });

    const productId = req.params.id;
    const { quantity, orderId } = req.body;
    
    if (!quantity || !orderId) {
      span.recordException(new Error('Missing required fields'));
      span.setStatus({ code: 2, message: 'Missing required fields' });
      return res.status(400).json({ 
        error: 'quantity and orderId are required' 
      });
    }

    const productIndex = inventory.findIndex(item => item.id === productId);
    
    if (productIndex === -1) {
      span.setAttributes({
        'product.found': false
      });
      span.setStatus({ code: 2, message: 'Product not found' });
      return res.status(404).json({ error: 'Product not found' });
    }

    const product = inventory[productIndex];
    
    if (product.stock < quantity) {
      span.setAttributes({
        'product.found': true,
        'reservation.quantity': quantity,
        'reservation.available_stock': product.stock,
        'reservation.success': false
      });
      span.setStatus({ code: 2, message: 'Insufficient stock' });
      return res.status(400).json({ 
        error: 'Insufficient stock',
        available: product.stock,
        requested: quantity
      });
    }

    // Reserve the items
    inventory[productIndex].stock -= quantity;
    inventory[productIndex].reserved += quantity;
    inventory[productIndex].available = inventory[productIndex].stock > 0;

    span.setAttributes({
      'product.found': true,
      'reservation.quantity': quantity,
      'reservation.order_id': orderId,
      'reservation.success': true,
      'product.new_stock': inventory[productIndex].stock,
      'product.total_reserved': inventory[productIndex].reserved
    });

    res.json({
      message: 'Inventory reserved successfully',
      productId,
      orderId,
      quantity,
      remainingStock: inventory[productIndex].stock,
      totalReserved: inventory[productIndex].reserved
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Reserve inventory error:', error.message);
    res.status(500).json({ 
      error: 'Failed to reserve inventory',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Release reserved inventory
app.post('/inventory/:id/release', (req, res) => {
  const span = tracer.startSpan('inventory.release');
  
  try {
    span.setAttributes({
      'http.method': 'POST',
      'http.route': '/inventory/:id/release',
      'service.name': 'backend-inventory',
      'product.id': req.params.id
    });

    const productId = req.params.id;
    const { quantity, orderId } = req.body;
    
    if (!quantity || !orderId) {
      span.recordException(new Error('Missing required fields'));
      span.setStatus({ code: 2, message: 'Missing required fields' });
      return res.status(400).json({ 
        error: 'quantity and orderId are required' 
      });
    }

    const productIndex = inventory.findIndex(item => item.id === productId);
    
    if (productIndex === -1) {
      span.setAttributes({
        'product.found': false
      });
      span.setStatus({ code: 2, message: 'Product not found' });
      return res.status(404).json({ error: 'Product not found' });
    }

    const product = inventory[productIndex];
    
    if (product.reserved < quantity) {
      span.setAttributes({
        'product.found': true,
        'release.quantity': quantity,
        'release.reserved_stock': product.reserved,
        'release.success': false
      });
      span.setStatus({ code: 2, message: 'Cannot release more than reserved' });
      return res.status(400).json({ 
        error: 'Cannot release more than reserved',
        reserved: product.reserved,
        requested: quantity
      });
    }

    // Release the items
    inventory[productIndex].stock += quantity;
    inventory[productIndex].reserved -= quantity;
    inventory[productIndex].available = true;

    span.setAttributes({
      'product.found': true,
      'release.quantity': quantity,
      'release.order_id': orderId,
      'release.success': true,
      'product.new_stock': inventory[productIndex].stock,
      'product.total_reserved': inventory[productIndex].reserved
    });

    res.json({
      message: 'Inventory released successfully',
      productId,
      orderId,
      quantity,
      newStock: inventory[productIndex].stock,
      totalReserved: inventory[productIndex].reserved
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Release inventory error:', error.message);
    res.status(500).json({ 
      error: 'Failed to release inventory',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Update product stock
app.patch('/inventory/:id/stock', (req, res) => {
  const span = tracer.startSpan('inventory.update_stock');
  
  try {
    span.setAttributes({
      'http.method': 'PATCH',
      'http.route': '/inventory/:id/stock',
      'service.name': 'backend-inventory',
      'product.id': req.params.id
    });

    const productId = req.params.id;
    const { stock } = req.body;
    
    if (stock === undefined || stock < 0) {
      span.recordException(new Error('Invalid stock value'));
      span.setStatus({ code: 2, message: 'Invalid stock value' });
      return res.status(400).json({ 
        error: 'Valid stock value is required (>= 0)' 
      });
    }

    const productIndex = inventory.findIndex(item => item.id === productId);
    
    if (productIndex === -1) {
      span.setAttributes({
        'product.found': false
      });
      span.setStatus({ code: 2, message: 'Product not found' });
      return res.status(404).json({ error: 'Product not found' });
    }

    const oldStock = inventory[productIndex].stock;
    inventory[productIndex].stock = stock;
    inventory[productIndex].available = stock > 0;

    span.setAttributes({
      'product.found': true,
      'product.old_stock': oldStock,
      'product.new_stock': stock,
      'product.available': inventory[productIndex].available
    });

    res.json({
      message: 'Stock updated successfully',
      productId,
      oldStock,
      newStock: stock,
      available: inventory[productIndex].available
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Update stock error:', error.message);
    res.status(500).json({ 
      error: 'Failed to update stock',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Simulate high error rate endpoint for testing
app.get('/inventory/test-error', (req, res) => {
  const span = tracer.startSpan('inventory.test_error');
  
  try {
    span.setAttributes({
      'http.method': 'GET',
      'http.route': '/inventory/test-error',
      'service.name': 'backend-inventory',
      'test.error_simulation': true
    });

    // 50% chance of error for this service (higher than others)
    if (Math.random() < 0.5) {
      const error = new Error('Simulated inventory service error - critical system failure');
      span.recordException(error);
      span.setStatus({ code: 2, message: error.message });
      throw error;
    }

    span.setAttributes({
      'test.result': 'success'
    });

    res.json({
      message: 'Test endpoint - no error this time',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Test error:', error.message);
    res.status(500).json({ 
      error: 'Simulated error occurred',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Inventory service running on port ${PORT}`);
  console.log(`Inventory items loaded: ${inventory.length}`);
});

const express = require('express');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { trace } = require('@opentelemetry/api');

const app = express();
const PORT = process.env.PORT || 3002;

// Service endpoints
const INVENTORY_SERVICE = process.env.INVENTORY_SERVICE_URL || 'http://backend-inventory:3003';

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// Get tracer
const tracer = trace.getTracer('backend-orders-service', '1.0.0');

// Mock orders database
let orders = [
  {
    id: 'order-1',
    userId: 'user-1',
    productId: 'product-1',
    quantity: 2,
    status: 'completed',
    total: 199.98,
    createdAt: new Date(Date.now() - 86400000).toISOString() // 1 day ago
  },
  {
    id: 'order-2',
    userId: 'user-2',
    productId: 'product-2',
    quantity: 1,
    status: 'pending',
    total: 149.99,
    createdAt: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
  }
];

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    service: 'backend-orders',
    timestamp: new Date().toISOString()
  });
});

// Service info endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Orders Service',
    service: 'backend-orders',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      orders: '/orders',
      createOrder: 'POST /orders',
      getOrder: '/orders/:id'
    }
  });
});

// Get all orders
app.get('/orders', (req, res) => {
  const span = tracer.startSpan('orders.get_all');
  
  try {
    span.setAttributes({
      'http.method': 'GET',
      'http.route': '/orders',
      'service.name': 'backend-orders'
    });

    // Simulate database query
    const dbSpan = tracer.startSpan('orders.database.select_all', { parent: span });
    try {
      // Simulate some processing time
      setTimeout(() => {}, Math.random() * 50 + 25);
      
      dbSpan.setAttributes({
        'db.operation': 'select',
        'db.table': 'orders',
        'db.rows_affected': orders.length
      });
    } catch (error) {
      dbSpan.recordException(error);
      dbSpan.setStatus({ code: 2, message: error.message });
      throw error;
    } finally {
      dbSpan.end();
    }

    span.setAttributes({
      'orders.count': orders.length
    });

    res.json({
      orders,
      count: orders.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Get orders error:', error.message);
    res.status(500).json({ 
      error: 'Failed to fetch orders',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Get specific order
app.get('/orders/:id', (req, res) => {
  const span = tracer.startSpan('orders.get_by_id');
  
  try {
    span.setAttributes({
      'http.method': 'GET',
      'http.route': '/orders/:id',
      'service.name': 'backend-orders',
      'order.id': req.params.id
    });

    const orderId = req.params.id;
    const order = orders.find(o => o.id === orderId);

    if (!order) {
      span.setAttributes({
        'order.found': false
      });
      span.setStatus({ code: 2, message: 'Order not found' });
      return res.status(404).json({ error: 'Order not found' });
    }

    span.setAttributes({
      'order.found': true,
      'order.status': order.status,
      'order.total': order.total
    });

    res.json(order);

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Get order error:', error.message);
    res.status(500).json({ 
      error: 'Failed to fetch order',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Create new order
app.post('/orders', async (req, res) => {
  const span = tracer.startSpan('orders.create');
  
  try {
    span.setAttributes({
      'http.method': 'POST',
      'http.route': '/orders',
      'service.name': 'backend-orders'
    });

    const { userId, productId, quantity } = req.body;
    
    if (!userId || !productId || !quantity) {
      span.recordException(new Error('Missing required fields'));
      span.setStatus({ code: 2, message: 'Missing required fields' });
      return res.status(400).json({ 
        error: 'userId, productId, and quantity are required' 
      });
    }

    span.setAttributes({
      'order.user_id': userId,
      'order.product_id': productId,
      'order.quantity': quantity
    });

    // Check inventory availability
    const inventorySpan = tracer.startSpan('orders.check_inventory', { parent: span });
    let inventoryData;
    try {
      const inventoryResponse = await axios.get(`${INVENTORY_SERVICE}/inventory/${productId}`);
      inventoryData = inventoryResponse.data;
      
      inventorySpan.setAttributes({
        'inventory.product_id': productId,
        'inventory.available': inventoryData.available,
        'inventory.stock': inventoryData.stock
      });

      if (!inventoryData.available || inventoryData.stock < quantity) {
        const error = new Error('Insufficient inventory');
        inventorySpan.recordException(error);
        inventorySpan.setStatus({ code: 2, message: error.message });
        span.recordException(error);
        span.setStatus({ code: 2, message: error.message });
        return res.status(400).json({ 
          error: 'Insufficient inventory',
          available: inventoryData.stock,
          requested: quantity
        });
      }
    } catch (error) {
      inventorySpan.recordException(error);
      inventorySpan.setStatus({ code: 2, message: error.message });
      throw new Error(`Inventory check failed: ${error.message}`);
    } finally {
      inventorySpan.end();
    }

    // Create order
    const orderId = uuidv4();
    const order = {
      id: orderId,
      userId,
      productId,
      quantity,
      status: 'pending',
      total: inventoryData.price * quantity,
      createdAt: new Date().toISOString()
    };

    // Simulate database insert
    const dbSpan = tracer.startSpan('orders.database.insert', { parent: span });
    try {
      // Simulate some processing time
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
      orders.push(order);
      
      dbSpan.setAttributes({
        'db.operation': 'insert',
        'db.table': 'orders',
        'db.rows_affected': 1
      });
    } catch (error) {
      dbSpan.recordException(error);
      dbSpan.setStatus({ code: 2, message: error.message });
      throw error;
    } finally {
      dbSpan.end();
    }

    // Update inventory (reserve items)
    const reserveSpan = tracer.startSpan('orders.reserve_inventory', { parent: span });
    try {
      await axios.post(`${INVENTORY_SERVICE}/inventory/${productId}/reserve`, {
        quantity,
        orderId
      });
      
      reserveSpan.setAttributes({
        'inventory.reserved': true,
        'inventory.quantity': quantity
      });
    } catch (error) {
      reserveSpan.recordException(error);
      reserveSpan.setStatus({ code: 2, message: error.message });
      // Don't fail the order creation if reservation fails
      console.warn('Failed to reserve inventory:', error.message);
    } finally {
      reserveSpan.end();
    }

    span.setAttributes({
      'order.created': true,
      'order.id': orderId,
      'order.total': order.total
    });

    res.status(201).json({
      message: 'Order created successfully',
      orderId: order.id,
      order,
      status: 'created'
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Create order error:', error.message);
    res.status(500).json({ 
      error: 'Failed to create order',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Update order status
app.patch('/orders/:id/status', (req, res) => {
  const span = tracer.startSpan('orders.update_status');
  
  try {
    span.setAttributes({
      'http.method': 'PATCH',
      'http.route': '/orders/:id/status',
      'service.name': 'backend-orders',
      'order.id': req.params.id
    });

    const orderId = req.params.id;
    const { status } = req.body;
    
    if (!status) {
      span.recordException(new Error('Missing status'));
      span.setStatus({ code: 2, message: 'Missing status' });
      return res.status(400).json({ error: 'Status is required' });
    }

    const orderIndex = orders.findIndex(o => o.id === orderId);
    
    if (orderIndex === -1) {
      span.setAttributes({
        'order.found': false
      });
      span.setStatus({ code: 2, message: 'Order not found' });
      return res.status(404).json({ error: 'Order not found' });
    }

    const oldStatus = orders[orderIndex].status;
    orders[orderIndex].status = status;
    orders[orderIndex].updatedAt = new Date().toISOString();

    span.setAttributes({
      'order.found': true,
      'order.old_status': oldStatus,
      'order.new_status': status
    });

    res.json({
      message: 'Order status updated',
      orderId,
      oldStatus,
      newStatus: status,
      order: orders[orderIndex]
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Update order status error:', error.message);
    res.status(500).json({ 
      error: 'Failed to update order status',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Simulate occasional errors for testing
app.get('/orders/test-error', (req, res) => {
  const span = tracer.startSpan('orders.test_error');
  
  try {
    span.setAttributes({
      'http.method': 'GET',
      'http.route': '/orders/test-error',
      'service.name': 'backend-orders',
      'test.error_simulation': true
    });

    // 25% chance of error
    if (Math.random() < 0.25) {
      const error = new Error('Simulated orders service error');
      span.recordException(error);
      span.setStatus({ code: 2, message: error.message });
      throw error;
    }

    span.setAttributes({
      'test.result': 'success'
    });

    res.json({
      message: 'Test endpoint - no error this time',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    span.recordException(error);
    span.setStatus({ code: 2, message: error.message });
    
    console.error('Test error:', error.message);
    res.status(500).json({ 
      error: 'Simulated error occurred',
      details: error.message
    });
  } finally {
    span.end();
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Orders service running on port ${PORT}`);
  console.log(`Inventory service endpoint: ${INVENTORY_SERVICE}`);
});

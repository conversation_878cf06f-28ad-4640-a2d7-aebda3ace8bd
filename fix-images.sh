#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Building and loading Docker images for Kind cluster..."

# Check if Kind cluster exists
if ! kind get clusters | grep -q "kotak-tailsampling"; then
    print_error "Kind cluster 'kotak-tailsampling' not found!"
    print_error "Please create the cluster first with: kind create cluster --config kind-cluster-config.yaml"
    exit 1
fi

# Build and load images
apps=("frontend" "backend-auth" "backend-orders" "backend-inventory")

for app in "${apps[@]}"; do
    print_status "Building $app image..."
    docker build -t "kotak-$app:latest" "apps/$app/"
    
    print_status "Loading $app image into Kind cluster..."
    kind load docker-image "kotak-$app:latest" --name kotak-tailsampling
    
    print_success "✓ $app image ready"
done

print_success "All images built and loaded successfully!"

# Restart the deployments to pull the new images
print_status "Restarting deployments to use the new images..."
kubectl rollout restart deployment -n kotak-demo

# Wait for pods to be ready
print_status "Waiting for pods to be ready..."
kubectl wait --for=condition=ready pod -l app=frontend -n kotak-demo --timeout=300s
kubectl wait --for=condition=ready pod -l app=backend-auth -n kotak-demo --timeout=300s
kubectl wait --for=condition=ready pod -l app=backend-orders -n kotak-demo --timeout=300s
kubectl wait --for=condition=ready pod -l app=backend-inventory -n kotak-demo --timeout=300s

print_success "All pods are now ready!"

# Show final status
print_status "Final pod status:"
kubectl get pods -n kotak-demo

# Kotak Tail Sampling Demo - Setup Summary

## What This Demo Provides

✅ **Kind Kubernetes cluster** with 2 nodes  
✅ **4 distributed Node.js applications** with OpenTelemetry auto-instrumentation  
✅ **Coralogix integration** for telemetry collection  
✅ **Tail sampling** with different percentages for different services  
✅ **Complete automation** with validation and testing scripts  

## Architecture Overview

```
Frontend (50% sampling) → Auth Service (75% sampling)
    ↓                         ↓
Orders Service (25% sampling) → Inventory Service (100% errors, 10% normal)
    ↓                         ↓
         OpenTelemetry Gateway (Tail Sampling)
                    ↓
               Coralogix Backend
```

## Quick Start Commands

1. **Check prerequisites:**
   ```bash
   ./check-setup.sh
   ```

2. **Set environment variables:**
   ```bash
   export CORALOGIX_API_KEY="your-coralogix-api-key"
   export CORALOGIX_DOMAIN="your-coralogix-domain"  # e.g., coralogix.com
   ```

3. **Run complete setup:**
   ```bash
   ./setup.sh
   ```

4. **Test applications:**
   ```bash
   ./scripts/test-endpoints.sh both
   ```

5. **Validate telemetry:**
   ```bash
   ./scripts/validate-telemetry.sh
   ```

## Service Endpoints

- **Frontend**: http://localhost:30000
- **Auth Service**: http://localhost:30001
- **Orders Service**: http://localhost:30002
- **Inventory Service**: http://localhost:30003

## Tail Sampling Configuration

| Service | Normal Traces | Error Traces | Policy Type |
|---------|---------------|--------------|-------------|
| Frontend | 50% | 100% | Probabilistic + Error |
| Auth | 75% | 100% | Probabilistic + Error |
| Orders | 25% | 100% | Probabilistic + Error |
| Inventory | 10% | 100% | Probabilistic + Error |
| Default | 20% | 100% | Probabilistic + Error |

## Key Features Demonstrated

### 1. Distributed Tracing
- Traces span across all 4 services
- Each service adds its own spans
- Parent-child relationships maintained

### 2. Tail Sampling
- Decision made after complete trace collection
- Service-specific sampling rates
- Error traces always sampled (100%)
- Load balancing ensures trace completeness

### 3. OpenTelemetry Auto-Instrumentation
- HTTP requests/responses
- Database operations (simulated)
- Custom spans and attributes
- Error tracking and exceptions

### 4. Kubernetes Integration
- DaemonSet for node-level collection
- Gateway deployment for processing
- Service discovery and metadata

## Testing Scenarios

### 1. Basic Health Checks
```bash
curl http://localhost:30000/health
curl http://localhost:30001/health
curl http://localhost:30002/health
curl http://localhost:30003/health
```

### 2. Authentication Flow
```bash
curl -X POST http://localhost:30001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"demo-user","password":"demo-pass"}'
```

### 3. Complete Workflow (All Services)
```bash
curl -X POST http://localhost:30000/api/workflow \
  -H "Content-Type: application/json" \
  -d '{"username":"demo-user","password":"demo-pass","productId":"product-1","quantity":1}'
```

### 4. Error Generation
```bash
curl http://localhost:30001/auth/test-error
curl http://localhost:30002/orders/test-error
curl http://localhost:30003/inventory/test-error
```

## Monitoring Commands

### Check Cluster Status
```bash
kubectl get nodes
kubectl get pods -A
kubectl get svc -n kotak-demo
```

### Monitor OpenTelemetry
```bash
kubectl logs -l "app.kubernetes.io/component=opentelemetry-gateway" -f
kubectl get pods -l "app.kubernetes.io/name=opentelemetry-collector"
```

### Application Logs
```bash
kubectl logs -n kotak-demo -l app=frontend -f
kubectl logs -n kotak-demo -l app=backend-auth -f
kubectl logs -n kotak-demo -l app=backend-orders -f
kubectl logs -n kotak-demo -l app=backend-inventory -f
```

## Expected Results in Coralogix

1. **Traces from all 4 services** with proper service names
2. **Different sampling rates** visible in trace volume
3. **100% of error traces** regardless of service
4. **Distributed traces** showing complete request flows
5. **Service maps** showing inter-service dependencies

## Cleanup

```bash
# Delete Kind cluster
kind delete cluster --name kotak-tailsampling

# Remove Docker images
docker rmi kotak-frontend:latest kotak-backend-auth:latest kotak-backend-orders:latest kotak-backend-inventory:latest
```

## Troubleshooting

### Common Issues

1. **Docker not running**: Start Docker Desktop
2. **Missing tools**: Install kind, kubectl, helm
3. **API key not set**: Export CORALOGIX_API_KEY
4. **Pods not ready**: Wait longer or check logs
5. **Port conflicts**: Ensure ports 30000-30003 are free

### Debug Commands

```bash
# Check setup
./check-setup.sh

# Validate telemetry
./scripts/validate-telemetry.sh

# Generate test load
./scripts/test-endpoints.sh load

# Check OpenTelemetry config
kubectl describe configmap -l "app.kubernetes.io/name=opentelemetry-collector"
```

## Next Steps

1. **Customize sampling policies** in `otel/coralogix-values.yaml`
2. **Add more services** following the same pattern
3. **Implement custom metrics** and logs
4. **Experiment with different trace scenarios**
5. **Monitor performance impact** of different sampling rates

## Files Overview

- `setup.sh` - Main automation script
- `check-setup.sh` - Prerequisites verification
- `kind-cluster-config.yaml` - Kubernetes cluster configuration
- `otel/coralogix-values.yaml` - OpenTelemetry and tail sampling configuration
- `apps/*/` - Node.js applications with OpenTelemetry instrumentation
- `k8s/apps/` - Kubernetes deployment manifests
- `scripts/test-endpoints.sh` - Testing and load generation
- `scripts/validate-telemetry.sh` - Telemetry validation

This demo provides a complete, production-like setup for understanding and experimenting with OpenTelemetry tail sampling in a Kubernetes environment.
